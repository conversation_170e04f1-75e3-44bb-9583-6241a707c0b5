import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import axios from 'axios';
import toast from 'react-hot-toast';

const BatchDownload = () => {
  const { batchId } = useParams();
  const [batchData, setBatchData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchBatchData();
  }, [batchId]);

  const fetchBatchData = async () => {
    try {
      // This would be implemented in the backend
      // For now, we'll show a placeholder
      setLoading(false);
      
      // Mock data for demonstration
      setBatchData({
        batchId: batchId,
        files: [
          {
            id: '1',
            originalName: 'document.pdf',
            size: 1024000,
            mimetype: 'application/pdf',
            downloadLink: '#',
            qrCode: null
          },
          {
            id: '2',
            originalName: 'image.jpg',
            size: 2048000,
            mimetype: 'image/jpeg',
            downloadLink: '#',
            qrCode: null
          }
        ],
        zipDownloadLink: '#',
        uploadDate: new Date().toISOString(),
        description: 'Sample batch upload'
      });
    } catch (error) {
      console.error('Error fetching batch data:', error);
      setError('Failed to load batch data');
      setLoading(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimetype) => {
    if (mimetype.startsWith('image/')) return '🖼️';
    if (mimetype.startsWith('video/')) return '🎥';
    if (mimetype.startsWith('audio/')) return '🎵';
    if (mimetype.includes('pdf')) return '📄';
    if (mimetype.includes('zip') || mimetype.includes('rar')) return '📦';
    return '📁';
  };

  const downloadAll = () => {
    if (batchData?.zipDownloadLink) {
      window.open(batchData.zipDownloadLink, '_blank');
    } else {
      toast.error('ZIP download not available');
    }
  };

  if (loading) {
    return (
      <div className="page batch-download-page">
        <div className="container">
          <div className="loading-state">
            <div className="spinner-large"></div>
            <p>Loading batch files...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !batchData) {
    return (
      <div className="page batch-download-page">
        <div className="container">
          <div className="error-state">
            <div className="error-icon">❌</div>
            <h2>Batch Not Found</h2>
            <p>The requested batch of files could not be found or may have expired.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="page batch-download-page"
    >
      <div className="container">
        <div className="batch-header">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <h1>📦 Batch Download</h1>
            <p>Download multiple files from this shared batch</p>
            {batchData.description && (
              <div className="batch-description">
                <strong>Description:</strong> {batchData.description}
              </div>
            )}
          </motion.div>
        </div>

        <div className="batch-info">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="batch-stats"
          >
            <div className="stat-item">
              <span className="stat-number">{batchData.files.length}</span>
              <span className="stat-label">Files</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">
                {formatFileSize(batchData.files.reduce((sum, file) => sum + file.size, 0))}
              </span>
              <span className="stat-label">Total Size</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">
                {new Date(batchData.uploadDate).toLocaleDateString()}
              </span>
              <span className="stat-label">Uploaded</span>
            </div>
          </motion.div>

          {batchData.zipDownloadLink && (
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="download-all-section"
            >
              <button onClick={downloadAll} className="btn btn-primary btn-large">
                📦 Download All as ZIP
              </button>
              <p>Get all files in a single ZIP archive</p>
            </motion.div>
          )}
        </div>

        <div className="files-section">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <h2>Individual Files</h2>
            <div className="files-grid">
              {batchData.files.map((file, index) => (
                <motion.div
                  key={file.id}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.9 + index * 0.1 }}
                  className="file-download-card"
                >
                  <div className="file-icon">{getFileIcon(file.mimetype)}</div>
                  <div className="file-info">
                    <h3>{file.originalName}</h3>
                    <p>{formatFileSize(file.size)}</p>
                    <span className="file-type">{file.mimetype}</span>
                  </div>
                  <div className="file-actions">
                    <a
                      href={file.downloadLink}
                      className="btn btn-primary"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      ⬇️ Download
                    </a>
                    {file.qrCode && (
                      <button
                        onClick={() => {
                          const newWindow = window.open();
                          newWindow.document.write(`
                            <html>
                              <head><title>QR Code - ${file.originalName}</title></head>
                              <body style="text-align: center; padding: 20px; font-family: Arial, sans-serif;">
                                <h2>${file.originalName}</h2>
                                <img src="${file.qrCode}" alt="QR Code" style="max-width: 300px; border: 1px solid #ddd; padding: 20px;">
                                <p>Scan this QR code with your mobile device to download the file</p>
                                <p style="color: #666; font-size: 14px;">Size: ${formatFileSize(file.size)}</p>
                              </body>
                            </html>
                          `);
                        }}
                        className="btn btn-outline"
                      >
                        📱 QR Code
                      </button>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        <div className="batch-footer">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 1.2 }}
            className="footer-content"
          >
            <h3>📱 Mobile Download</h3>
            <p>
              Each file has a QR code for easy mobile downloading. 
              Simply scan the QR code with your phone's camera to download directly to your device.
            </p>
            
            <div className="security-note">
              <h4>🔒 Security Notice</h4>
              <p>
                These files are shared securely through QuickShare. 
                Download links are unique and cannot be guessed. 
                Files may expire based on the uploader's settings.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default BatchDownload;
