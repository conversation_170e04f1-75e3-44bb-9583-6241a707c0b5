/* QuickShare Professional SaaS Platform - Modern Tailwind CSS */
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

/* Custom CSS Variables */
:root {
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-success: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
  
  /* Glass morphism */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* Dark mode glass */
  --glass-bg-dark: rgba(15, 23, 42, 0.25);
  --glass-border-dark: rgba(255, 255, 255, 0.1);
  --glass-shadow-dark: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

/* Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Component Styles */
@layer components {
  /* Glass Morphism Effect */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }
  
  .dark .glass {
    background: var(--glass-bg-dark);
    border: 1px solid var(--glass-border-dark);
    box-shadow: var(--glass-shadow-dark);
  }
  
  /* Professional Buttons */
  .btn-professional {
    @apply px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-opacity-50;
  }
  
  .btn-primary-pro {
    @apply btn-professional bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 focus:ring-blue-500;
  }
  
  .btn-secondary-pro {
    @apply btn-professional bg-gradient-to-r from-pink-500 to-rose-500 text-white hover:from-pink-600 hover:to-rose-600 focus:ring-pink-500;
  }
  
  .btn-outline-pro {
    @apply btn-professional border-2 border-gray-300 text-gray-700 hover:border-blue-500 hover:text-blue-600 dark:border-gray-600 dark:text-gray-300 dark:hover:border-blue-400 dark:hover:text-blue-400 focus:ring-blue-500;
  }
  
  /* Professional Cards */
  .card-professional {
    @apply bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-2xl hover:-translate-y-1;
  }
  
  .card-glass {
    @apply glass rounded-2xl transition-all duration-300 hover:shadow-2xl hover:-translate-y-1;
  }
  
  /* Professional Form Inputs */
  .input-professional {
    @apply w-full px-4 py-3 rounded-xl border-2 border-gray-300 focus:border-blue-500 focus:ring-4 focus:ring-blue-500 focus:ring-opacity-20 transition-all duration-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white dark:focus:border-blue-400;
  }
  
  /* Loading Animations */
  .spinner-professional {
    @apply w-8 h-8 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin;
  }
  
  .spinner-large-professional {
    @apply w-12 h-12 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin;
  }
  
  /* Professional Navigation */
  .nav-professional {
    @apply glass fixed top-0 left-0 right-0 z-50 transition-all duration-300;
  }
  
  /* Professional Hero Section */
  .hero-professional {
    background: var(--gradient-primary);
    @apply text-white relative overflow-hidden;
  }
  
  .hero-professional::before {
    content: '';
    @apply absolute inset-0 bg-black bg-opacity-10;
  }
  
  /* Professional File Cards */
  .file-card-professional {
    @apply card-professional p-6 flex items-center space-x-4 cursor-pointer transition-all duration-300 hover:border-blue-500 hover:shadow-glow;
  }
  
  .file-card-selected {
    @apply border-green-500 bg-green-50 dark:bg-green-900 dark:bg-opacity-20;
  }
  
  /* Professional Upload Zone */
  .upload-zone-professional {
    @apply border-3 border-dashed border-gray-300 rounded-2xl p-12 text-center cursor-pointer transition-all duration-300 hover:border-blue-500 hover:bg-blue-50 dark:border-gray-600 dark:hover:border-blue-400 dark:hover:bg-blue-900 dark:hover:bg-opacity-20;
  }
  
  .upload-zone-active {
    @apply border-blue-500 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 scale-105;
  }
  
  /* Professional Progress Bar */
  .progress-professional {
    @apply w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700 overflow-hidden;
  }
  
  .progress-fill-professional {
    @apply h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-300 relative overflow-hidden;
  }
  
  .progress-fill-professional::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-30 to-transparent animate-shimmer;
  }
}

/* Utility Classes */
@layer utilities {
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }
  
  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }
  
  .bg-gradient-accent {
    background: var(--gradient-accent);
  }
  
  .bg-gradient-success {
    background: var(--gradient-success);
  }
  
  .text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.4);
  }
}

/* Legacy support for existing components */
.spinner {
  @apply spinner-professional;
}

.spinner-large {
  @apply spinner-large-professional;
}

/* Center content properly */
.container-center {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.page-center {
  @apply min-h-screen flex flex-col items-center justify-center;
}

/* Professional animations */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}
