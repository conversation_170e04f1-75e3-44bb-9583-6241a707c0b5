@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@layer base {
  body {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg;
  }
  
  .btn-secondary {
    @apply bg-gradient-to-r from-pink-500 to-rose-500 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg;
  }
  
  .btn-outline {
    @apply border-2 border-gray-300 text-gray-700 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:border-blue-500 hover:text-blue-600 hover:scale-105;
  }
  
  .card {
    @apply bg-white rounded-2xl shadow-xl border border-gray-200 transition-all duration-300 hover:shadow-2xl hover:-translate-y-1;
  }
  
  .glass {
    @apply bg-white bg-opacity-25 backdrop-blur-md border border-white border-opacity-20;
  }
  
  .spinner {
    @apply w-8 h-8 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin;
  }
  
  .spinner-large {
    @apply w-12 h-12 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin;
  }
  
  .container-center {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .hero-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .bg-gradient-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }
  
  .bg-gradient-accent {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
  
  .text-gradient {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

@layer utilities {
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.4);
  }
}
