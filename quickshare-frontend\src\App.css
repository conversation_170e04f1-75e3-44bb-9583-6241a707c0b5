/* QuickShare Professional SaaS Platform - Modern CSS */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Custom CSS Variables */
:root {
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-success: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);

  /* Glass morphism */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Dark mode glass */
  --glass-bg-dark: rgba(15, 23, 42, 0.25);
  --glass-border-dark: rgba(255, 255, 255, 0.1);
  --glass-shadow-dark: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: "Inter", system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f8fafc;
  color: #1e293b;
  line-height: 1.6;
}

/* Modern Professional Components */
.container-center {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.card-professional {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.card-professional:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.btn-professional {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
  transform: translateY(0);
}

.btn-professional:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-primary-pro {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
}

.btn-primary-pro:hover {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}

.btn-secondary-pro {
  background: linear-gradient(135deg, #ec4899, #f43f5e);
  color: white;
}

.btn-secondary-pro:hover {
  background: linear-gradient(135deg, #db2777, #e11d48);
}

.btn-outline-pro {
  background: transparent;
  color: #374151;
  border: 2px solid #d1d5db;
}

.btn-outline-pro:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #f0f9ff;
}

.hero-professional {
  background: var(--gradient-primary);
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-professional::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
}

.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.spinner-professional {
  width: 2rem;
  height: 2rem;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-large-professional {
  width: 3rem;
  height: 3rem;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.file-card-professional {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 2px solid #e5e7eb;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-card-professional:hover {
  border-color: #3b82f6;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.file-card-selected {
  border-color: #10b981;
  background: #f0fdf4;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

/* Legacy support for existing components */
.spinner {
  width: 2rem;
  height: 2rem;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-large {
  width: 3rem;
  height: 3rem;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Utility Classes */
.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-accent {
  background: var(--gradient-accent);
}

.bg-gradient-success {
  background: var(--gradient-success);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.shadow-glow-lg {
  box-shadow: 0 0 40px rgba(59, 130, 246, 0.4);
}

/* Professional animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Modern Layout Classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}
.space-x-4 > * + * {
  margin-left: 1rem;
}

/* Grid Classes */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

/* Responsive Grid */
@media (min-width: 768px) {
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .md\\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .lg\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .lg\\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Spacing Classes */
.p-2 {
  padding: 0.5rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.m-0 {
  margin: 0;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-12 {
  margin-bottom: 3rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

/* Width and Height */
.w-5 {
  width: 1.25rem;
}
.h-5 {
  height: 1.25rem;
}

.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-6xl {
  max-width: 72rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Text Classes */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}

.text-xs {
  font-size: 0.75rem;
}
.text-sm {
  font-size: 0.875rem;
}
.text-lg {
  font-size: 1.125rem;
}
.text-xl {
  font-size: 1.25rem;
}
.text-2xl {
  font-size: 1.5rem;
}
.text-3xl {
  font-size: 1.875rem;
}
.text-4xl {
  font-size: 2.25rem;
}
.text-5xl {
  font-size: 3rem;
}

.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}
.font-black {
  font-weight: 900;
}

/* Colors */
.text-gray-500 {
  color: #6b7280;
}
.text-gray-600 {
  color: #4b5563;
}
.text-gray-700 {
  color: #374151;
}
.text-gray-900 {
  color: #111827;
}
.text-blue-600 {
  color: #2563eb;
}
.text-green-600 {
  color: #16a34a;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}
.bg-gray-50 {
  background-color: #f9fafb;
}
.bg-blue-50 {
  background-color: #eff6ff;
}
.bg-green-50 {
  background-color: #f0fdf4;
}

/* Border */
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-gray-300 {
  border-color: #d1d5db;
}
.border-blue-200 {
  border-color: #bfdbfe;
}
.border-green-500 {
  border-color: #22c55e;
}

.rounded {
  border-radius: 0.25rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-xl {
  border-radius: 0.75rem;
}

/* Ring */
.ring-2 {
  box-shadow: 0 0 0 2px var(--tw-ring-color);
}
.ring-green-500 {
  --tw-ring-color: #22c55e;
}

/* Focus */
.focus\\:ring-green-500:focus {
  --tw-ring-color: #22c55e;
}
.focus\\:ring-2:focus {
  box-shadow: 0 0 0 2px var(--tw-ring-color);
}

/* Cursor */
.cursor-pointer {
  cursor: pointer;
}
.cursor-not-allowed {
  cursor: not-allowed;
}

/* Opacity */
.opacity-50 {
  opacity: 0.5;
}

/* Transform */
.transform {
  transform: translateX(var(--tw-translate-x, 0))
    translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0))
    skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0))
    scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1));
}

/* Truncate */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Min Width */
.min-w-0 {
  min-width: 0px;
}

/* Flex */
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}

/* Professional Input Styles */
.input-professional {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  border: 2px solid #d1d5db;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.input-professional:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* Professional Checkbox */
.checkbox-professional {
  width: 1.25rem;
  height: 1.25rem;
  color: #16a34a;
  background-color: #f3f4f6;
  border-color: #d1d5db;
  border-radius: 0.25rem;
  cursor: pointer;
}

.checkbox-professional:focus {
  box-shadow: 0 0 0 2px #16a34a;
}
