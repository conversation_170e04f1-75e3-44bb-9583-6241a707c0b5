/* QuickShare Enhanced App Styles */

/* CSS Variables for Theming */
:root {
  /* Light Theme */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --card-bg: #ffffff;
  --overlay-bg: rgba(255, 255, 255, 0.95);

  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --text-inverse: #ffffff;

  --border-color: #e2e8f0;
  --border-hover: #cbd5e1;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;

  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --card-bg: #1e293b;
  --overlay-bg: rgba(15, 23, 42, 0.95);

  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;

  --border-color: #334155;
  --border-hover: #475569;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: "Inter", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-normal),
    color var(--transition-normal);
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Page Styles */
.page {
  flex: 1;
  padding-top: 80px; /* Account for fixed navbar */
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
}

.loading-content h2 {
  margin: 1rem 0 0.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 2rem;
  font-weight: 700;
}

.loading-content p {
  color: var(--text-secondary);
  margin: 0;
}

/* Spinners */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--info-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-large {
  width: 60px;
  height: 60px;
  border: 6px solid var(--border-color);
  border-top: 6px solid var(--info-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Gradient Text */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-hover);
  transform: translateY(-1px);
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1rem;
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

/* Cards */
.card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Form Elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--info-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
}

.form-error {
  color: var(--error-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Navbar Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--overlay-bg);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: all var(--transition-normal);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.navbar-logo {
  text-decoration: none;
  color: var(--text-primary);
}

.logo-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  font-size: 1.5rem;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.navbar-link {
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
}

.navbar-link:hover,
.navbar-link.active {
  color: var(--text-primary);
  background: var(--bg-secondary);
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-toggle {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.theme-toggle:hover {
  background: var(--bg-secondary);
}

.auth-buttons {
  display: flex;
  gap: 0.5rem;
}

.user-menu {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.user-button:hover {
  background: var(--bg-secondary);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.premium-badge {
  font-size: 0.75rem;
  color: var(--warning-color);
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 0.75rem;
  color: var(--text-muted);
  transition: transform var(--transition-fast);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  min-width: 280px;
  z-index: 1001;
  overflow: hidden;
}

.dropdown-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.user-details strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.user-details span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.storage-info {
  margin-top: 0.75rem;
}

.storage-bar {
  width: 100%;
  height: 6px;
  background: var(--border-color);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.storage-fill {
  height: 100%;
  background: var(--primary-gradient);
  transition: width var(--transition-normal);
}

.storage-text {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.dropdown-menu {
  padding: 0.5rem 0;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--text-primary);
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
}

.dropdown-item:hover {
  background: var(--bg-secondary);
}

.dropdown-item.logout {
  color: var(--error-color);
}

.dropdown-item.premium-upgrade {
  color: var(--warning-color);
  font-weight: 600;
}

.dropdown-divider {
  border: none;
  border-top: 1px solid var(--border-color);
  margin: 0.5rem 0;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  gap: 3px;
}

.mobile-menu-toggle span {
  width: 20px;
  height: 2px;
  background: var(--text-primary);
  transition: all var(--transition-fast);
}

.mobile-menu {
  background: var(--card-bg);
  border-top: 1px solid var(--border-color);
  padding: 1rem;
}

.mobile-link {
  display: block;
  padding: 0.75rem 0;
  text-decoration: none;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.mobile-link:last-child {
  border-bottom: none;
}

.mobile-link:hover {
  color: var(--info-color);
}

/* Hero Section */
.hero {
  background: var(--primary-gradient);
  color: white;
  padding: 4rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.1;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  margin: 0 0 1rem;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin: 0 0 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.feature-icon {
  font-size: 1.25rem;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 3rem;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Upload Section */
.upload-section {
  padding: 4rem 0;
  background: var(--bg-secondary);
}

.upload-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.upload-header {
  text-align: center;
  margin-bottom: 3rem;
}

.upload-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
  color: var(--text-primary);
}

.upload-header p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
}

.upload-options {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.upload-status {
  text-align: center;
  padding: 3rem 2rem;
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

.upload-status h3 {
  margin: 1rem 0 0.5rem;
  color: var(--text-primary);
  font-size: 1.5rem;
}

.upload-status p {
  color: var(--text-secondary);
  margin: 0;
}

/* Features Section */
.features-section {
  padding: 4rem 0;
  background: var(--bg-primary);
}

.features-grid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 3rem;
  color: var(--text-primary);
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 2rem;
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.feature-card .feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: var(--text-primary);
}

.feature-card p {
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: 4rem 0;
  background: var(--bg-tertiary);
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 1rem;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
  color: var(--text-primary);
}

.cta-content p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0 0 2rem;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/* File Upload Styles */
.file-upload {
  width: 100%;
}

.upload-area {
  border: 3px dashed var(--border-color);
  border-radius: var(--radius-xl);
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  background: var(--card-bg);
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.upload-area:hover::before {
  opacity: 0.05;
}

.upload-area:hover {
  border-color: var(--info-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.upload-area.drag-active {
  border-color: var(--info-color);
  background: var(--bg-secondary);
  transform: scale(1.02);
}

.upload-area.drag-active::before {
  opacity: 0.1;
}

.upload-content {
  position: relative;
  z-index: 1;
}

.upload-content .upload-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
  opacity: 0.7;
}

.upload-content h3 {
  color: var(--text-primary);
  margin: 0 0 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.upload-content p {
  color: var(--text-secondary);
  margin: 0.5rem 0;
  font-size: 0.875rem;
}

/* Progress Bar */
.progress-container {
  margin-top: 2rem;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: var(--primary-gradient);
  transition: width var(--transition-normal);
  border-radius: 4px;
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-container p {
  color: var(--text-primary);
  font-weight: 600;
  margin: 0;
}

/* Share Link Styles */
.share-link {
  text-align: center;
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
}

.success-message {
  margin-bottom: 2rem;
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
}

.success-message h2 {
  color: var(--success-color);
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.file-info {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.file-icon {
  font-size: 3rem;
}

.file-meta {
  text-align: left;
  flex: 1;
}

.file-meta h3 {
  margin: 0 0 0.5rem;
  color: var(--text-primary);
  font-size: 1.2rem;
  word-break: break-all;
}

.file-meta p {
  margin: 0.25rem 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.download-link-section {
  margin-bottom: 2rem;
}

.download-link-section h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.link-container {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.link-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: monospace;
}

.copy-button {
  padding: 0.75rem 1.5rem;
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-weight: 600;
  transition: all var(--transition-normal);
  white-space: nowrap;
}

.copy-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.copy-button.copied {
  background: var(--success-color);
}

.link-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.test-button,
.upload-another-button {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  text-decoration: none;
  font-weight: 600;
  transition: all var(--transition-normal);
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
}

.test-button {
  background: var(--info-color);
  color: white;
}

.test-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.upload-another-button {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.upload-another-button:hover {
  background: var(--bg-tertiary);
  transform: translateY(-1px);
}

.security-note {
  background: var(--warning-color);
  background: linear-gradient(
    135deg,
    rgba(245, 158, 11, 0.1),
    rgba(245, 158, 11, 0.05)
  );
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-radius: var(--radius-lg);
  padding: 1rem;
  margin-top: 1.5rem;
}

.security-note p {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.875rem;
}

/* Footer */
.app-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  margin-top: auto;
  padding: 3rem 0 1rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.footer-section h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 1rem;
  color: var(--text-primary);
}

.footer-section h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem;
  color: var(--text-primary);
}

.footer-section p {
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.footer-bottom {
  max-width: 1200px;
  margin: 2rem auto 0;
  padding: 1rem;
  text-align: center;
  border-top: 1px solid var(--border-color);
}

.footer-bottom p {
  color: var(--text-muted);
  margin: 0;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .hero {
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-features {
    gap: 1rem;
  }

  .hero-stats {
    gap: 2rem;
  }

  .upload-section {
    padding: 2rem 0;
  }

  .upload-header h2 {
    font-size: 2rem;
  }

  .upload-area {
    padding: 2rem 1rem;
  }

  .features-section {
    padding: 2rem 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .features-list {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .cta-section {
    padding: 2rem 0;
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .link-container {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .test-button,
  .upload-another-button {
    width: 100%;
    max-width: 250px;
  }

  .file-details {
    flex-direction: column;
    text-align: center;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .user-dropdown {
    min-width: 250px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }

  .page {
    padding-top: 70px;
  }

  .hero-content {
    padding: 0 0.5rem;
  }

  .upload-container {
    padding: 0 0.5rem;
  }

  .features-grid {
    padding: 0 0.5rem;
  }

  .cta-content {
    padding: 0 0.5rem;
  }

  .btn-large {
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
  }

  .navbar-container {
    padding: 0 0.5rem;
  }

  .logo-text {
    display: none;
  }

  .auth-buttons {
    gap: 0.25rem;
  }

  .auth-buttons .btn {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
}

/* Print Styles */
@media print {
  .navbar,
  .mobile-menu,
  .upload-area,
  .action-buttons,
  .app-footer {
    display: none;
  }

  .page {
    padding-top: 0;
  }

  body {
    background: white;
    color: black;
  }

  .card {
    border: 1px solid #ccc;
    box-shadow: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-secondary: #000000;
    --shadow-sm: none;
    --shadow-md: none;
    --shadow-lg: none;
    --shadow-xl: none;
  }

  [data-theme="dark"] {
    --border-color: #ffffff;
    --text-secondary: #ffffff;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .animate-pulse {
    animation: none;
  }

  .progress-fill::after {
    animation: none;
  }
}

/* Auth Pages */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  padding: 2rem 1rem;
}

.auth-container {
  width: 100%;
  max-width: 500px;
}

.auth-card {
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.auth-header p {
  color: var(--text-secondary);
  margin: 0;
}

.auth-form {
  margin-bottom: 1.5rem;
}

.form-input.error {
  border-color: var(--error-color);
}

.btn-full {
  width: 100%;
  justify-content: center;
}

.auth-footer {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.auth-link {
  color: var(--info-color);
  text-decoration: none;
  font-weight: 600;
}

.auth-link:hover {
  text-decoration: underline;
}

.auth-features,
.auth-benefits {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.auth-features h3,
.auth-benefits h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: var(--text-primary);
  text-align: center;
}

.auth-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.auth-features li {
  padding: 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.benefit-icon {
  font-size: 1.25rem;
}

/* Dashboard Page */
.dashboard-page {
  padding: 2rem 0;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 3rem;
}

.dashboard-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.dashboard-header p {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  text-align: center;
  transition: all var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.stat-content p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.storage-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 2rem;
  margin-bottom: 3rem;
}

.storage-card h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: var(--text-primary);
}

.storage-visual {
  margin-bottom: 1rem;
}

.storage-bar-large {
  width: 100%;
  height: 12px;
  background: var(--border-color);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.storage-fill-large {
  height: 100%;
  background: var(--primary-gradient);
  transition: width var(--transition-normal);
  border-radius: 6px;
}

.storage-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.storage-info-text {
  color: var(--text-secondary);
  margin: 1rem 0;
}

.quick-actions {
  margin-bottom: 3rem;
}

.quick-actions h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: var(--text-primary);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  text-decoration: none;
  color: var(--text-primary);
  transition: all var(--transition-normal);
  text-align: center;
}

.action-card:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--info-color);
}

.action-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.action-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
}

.action-card p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.recent-files {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 2rem;
}

.recent-files h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: var(--text-primary);
}

.files-placeholder {
  text-align: center;
  padding: 3rem 1rem;
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.files-placeholder h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.files-placeholder p {
  color: var(--text-secondary);
  margin: 0 0 1.5rem;
}

.loading-state,
.error-state {
  text-align: center;
  padding: 4rem 2rem;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.error-state h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.error-state p {
  color: var(--text-secondary);
  margin: 0;
}

/* Bulk Upload Page */
.bulk-upload-page {
  padding: 2rem 0;
}

.bulk-upload-header {
  text-align: center;
  margin-bottom: 3rem;
}

.bulk-upload-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.bulk-upload-header p {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin: 0;
}

.bulk-upload-container {
  max-width: 800px;
  margin: 0 auto;
}

.dropzone {
  border: 3px dashed var(--border-color);
  border-radius: var(--radius-xl);
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  background: var(--card-bg);
  margin-bottom: 2rem;
}

.dropzone:hover,
.dropzone.active {
  border-color: var(--info-color);
  background: var(--bg-secondary);
  transform: translateY(-2px);
}

.dropzone.has-files {
  border-style: solid;
  border-color: var(--success-color);
}

.dropzone-content {
  pointer-events: none;
}

.dropzone-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.dropzone h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: var(--text-primary);
}

.dropzone p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.files-list {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.files-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.files-grid {
  display: grid;
  gap: 1rem;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  position: relative;
}

.file-item .file-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
  color: var(--text-primary);
  word-break: break-all;
}

.file-info p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

.file-progress {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-progress .progress-bar {
  flex: 1;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
}

.file-progress .progress-fill {
  height: 100%;
  background: var(--success-color);
  transition: width var(--transition-normal);
}

.file-progress span {
  font-size: 0.75rem;
  color: var(--text-secondary);
  min-width: 3rem;
  text-align: right;
}

.file-status {
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 0.25rem;
}

.file-status.success {
  color: var(--success-color);
}

.file-status.error {
  color: var(--error-color);
}

.remove-file {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--error-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.remove-file:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.upload-options {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 2rem;
}

.upload-options h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: var(--text-primary);
}

.options-grid {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.option-item input[type="checkbox"] {
  margin-right: 0.5rem;
}

.option-item label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.option-item .form-input {
  margin-top: 0.5rem;
}

/* Upload Success */
.upload-success {
  text-align: center;
  padding: 2rem 0;
}

.success-content {
  max-width: 800px;
  margin: 0 auto;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 3rem 2rem;
}

.success-content .success-icon {
  font-size: 5rem;
  margin-bottom: 1rem;
}

.success-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  color: var(--success-color);
}

.success-content p {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin: 0 0 2rem;
}

.upload-summary {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.summary-stat {
  text-align: center;
}

.summary-stat .stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.summary-stat .stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.download-options {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.individual-files {
  margin-top: 3rem;
  text-align: left;
}

.individual-files h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: var(--text-primary);
  text-align: center;
}

.file-result-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  margin-bottom: 1rem;
}

.file-result-card .file-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.file-result-card .file-info {
  flex: 1;
  min-width: 0;
}

.file-result-card h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
  color: var(--text-primary);
  word-break: break-all;
}

.file-result-card p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

.file-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

/* Batch Download Page */
.batch-download-page {
  padding: 2rem 0;
}

.batch-header {
  text-align: center;
  margin-bottom: 3rem;
}

.batch-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.batch-header p {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin: 0 0 1rem;
}

.batch-description {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: 1rem;
  margin-top: 1rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.batch-info {
  margin-bottom: 3rem;
}

.batch-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.download-all-section {
  text-align: center;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 2rem;
}

.download-all-section p {
  color: var(--text-secondary);
  margin: 1rem 0 0;
}

.files-section {
  margin-bottom: 3rem;
}

.files-section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: var(--text-primary);
  text-align: center;
}

.file-download-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all var(--transition-normal);
}

.file-download-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.file-download-card .file-icon {
  font-size: 3rem;
  flex-shrink: 0;
}

.file-download-card .file-info {
  flex: 1;
  min-width: 0;
}

.file-download-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
  word-break: break-all;
}

.file-download-card p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0 0 0.25rem;
}

.file-type {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-family: monospace;
}

.file-download-card .file-actions {
  display: flex;
  gap: 0.75rem;
  flex-shrink: 0;
}

.batch-footer {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: 2rem;
  text-align: center;
}

.footer-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: var(--text-primary);
}

.footer-content p {
  color: var(--text-secondary);
  margin: 0 0 1.5rem;
  line-height: 1.6;
}

.security-note h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.security-note p {
  margin: 0;
}
