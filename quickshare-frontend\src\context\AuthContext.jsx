import { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';

const AuthContext = createContext();

// API base URL
const API_BASE_URL = 'http://localhost:5000';

// Configure axios defaults
axios.defaults.baseURL = API_BASE_URL;

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(localStorage.getItem('quickshare-token'));

  // Set up axios interceptor for authentication
  useEffect(() => {
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete axios.defaults.headers.common['Authorization'];
    }
  }, [token]);

  // Check if user is authenticated on app load
  useEffect(() => {
    const checkAuth = async () => {
      if (token) {
        try {
          const response = await axios.get('/api/auth/me');
          if (response.data.success) {
            setUser(response.data.user);
          } else {
            // Invalid token, remove it
            localStorage.removeItem('quickshare-token');
            setToken(null);
          }
        } catch (error) {
          console.error('Auth check failed:', error);
          localStorage.removeItem('quickshare-token');
          setToken(null);
        }
      }
      setLoading(false);
    };

    checkAuth();
  }, [token]);

  // Login function
  const login = async (credentials) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/auth/login', credentials);
      
      if (response.data.success) {
        const { token: newToken, user: userData } = response.data;
        
        // Store token and user data
        localStorage.setItem('quickshare-token', newToken);
        setToken(newToken);
        setUser(userData);
        
        toast.success(`Welcome back, ${userData.displayName}!`);
        return { success: true };
      } else {
        toast.error(response.data.message || 'Login failed');
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      const message = error.response?.data?.message || 'Login failed. Please try again.';
      toast.error(message);
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/auth/register', userData);
      
      if (response.data.success) {
        const { token: newToken, user: newUser } = response.data;
        
        // Store token and user data
        localStorage.setItem('quickshare-token', newToken);
        setToken(newToken);
        setUser(newUser);
        
        toast.success(`Welcome to QuickShare, ${newUser.displayName}!`);
        return { success: true };
      } else {
        toast.error(response.data.message || 'Registration failed');
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      const message = error.response?.data?.message || 'Registration failed. Please try again.';
      toast.error(message);
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await axios.post('/api/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage and state
      localStorage.removeItem('quickshare-token');
      setToken(null);
      setUser(null);
      delete axios.defaults.headers.common['Authorization'];
      toast.success('Logged out successfully');
    }
  };

  // Update user data
  const updateUser = (userData) => {
    setUser(prev => ({ ...prev, ...userData }));
  };

  // Get user storage info
  const getStorageInfo = () => {
    if (!user) return null;
    
    return {
      used: user.storageUsed || 0,
      limit: user.storageLimit || 0,
      percentage: user.storageUsagePercentage || 0,
      remaining: (user.storageLimit || 0) - (user.storageUsed || 0),
      isPremium: user.isPremium || false
    };
  };

  // Get file size limit based on user type
  const getFileSizeLimit = () => {
    if (!user) return 100 * 1024 * 1024; // 100MB for guests
    if (user.isPremium) return 5 * 1024 * 1024 * 1024; // 5GB for premium
    return 500 * 1024 * 1024; // 500MB for registered users
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const value = {
    user,
    loading,
    token,
    login,
    register,
    logout,
    updateUser,
    getStorageInfo,
    getFileSizeLimit,
    formatFileSize,
    isAuthenticated: !!user,
    isPremium: user?.isPremium || false
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
