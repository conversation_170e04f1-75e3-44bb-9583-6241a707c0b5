import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import axios from 'axios';
import toast from 'react-hot-toast';
import { useAuth } from '../context/AuthContext';

const Dashboard = () => {
  const { user, formatFileSize, getStorageInfo } = useAuth();
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalFiles: 0,
    totalDownloads: 0,
    totalSize: 0
  });

  const storageInfo = getStorageInfo();

  useEffect(() => {
    fetchUserFiles();
  }, []);

  const fetchUserFiles = async () => {
    try {
      // This would be implemented in the backend
      // For now, we'll show a placeholder
      setLoading(false);
      
      // Mock data for demonstration
      setStats({
        totalFiles: user?.totalFiles || 0,
        totalDownloads: user?.totalDownloads || 0,
        totalSize: user?.storageUsed || 0
      });
    } catch (error) {
      console.error('Error fetching files:', error);
      toast.error('Failed to load files');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="page dashboard-page">
        <div className="container">
          <div className="loading-state">
            <div className="spinner-large"></div>
            <p>Loading your dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="page dashboard-page"
    >
      <div className="container">
        <div className="dashboard-header">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <h1>Welcome back, {user?.displayName}! 👋</h1>
            <p>Manage your files and track your sharing activity</p>
          </motion.div>
        </div>

        {/* Stats Cards */}
        <div className="stats-grid">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="stat-card"
          >
            <div className="stat-icon">📁</div>
            <div className="stat-content">
              <h3>{stats.totalFiles}</h3>
              <p>Files Uploaded</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="stat-card"
          >
            <div className="stat-icon">⬇️</div>
            <div className="stat-content">
              <h3>{stats.totalDownloads}</h3>
              <p>Total Downloads</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="stat-card"
          >
            <div className="stat-icon">💾</div>
            <div className="stat-content">
              <h3>{formatFileSize(stats.totalSize)}</h3>
              <p>Storage Used</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="stat-card"
          >
            <div className="stat-icon">{user?.isPremium ? '⭐' : '🚀'}</div>
            <div className="stat-content">
              <h3>{user?.isPremium ? 'Premium' : 'Free'}</h3>
              <p>Account Type</p>
            </div>
          </motion.div>
        </div>

        {/* Storage Usage */}
        {storageInfo && (
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.7 }}
            className="storage-card"
          >
            <h2>Storage Usage</h2>
            <div className="storage-visual">
              <div className="storage-bar-large">
                <div 
                  className="storage-fill-large"
                  style={{ width: `${storageInfo.percentage}%` }}
                ></div>
              </div>
              <div className="storage-details">
                <span>{formatFileSize(storageInfo.used)} used</span>
                <span>{formatFileSize(storageInfo.limit)} total</span>
              </div>
            </div>
            <p className="storage-info-text">
              {storageInfo.percentage < 80 ? (
                `You have ${formatFileSize(storageInfo.remaining)} remaining`
              ) : storageInfo.percentage < 95 ? (
                '⚠️ Storage is getting full'
              ) : (
                '🚨 Storage almost full - consider upgrading'
              )}
            </p>
            {!user?.isPremium && (
              <button className="btn btn-primary">
                ⭐ Upgrade to Premium for 5GB
              </button>
            )}
          </motion.div>
        )}

        {/* Quick Actions */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="quick-actions"
        >
          <h2>Quick Actions</h2>
          <div className="actions-grid">
            <Link to="/" className="action-card">
              <div className="action-icon">📤</div>
              <h3>Upload File</h3>
              <p>Upload a single file and get instant sharing link</p>
            </Link>

            <Link to="/bulk-upload" className="action-card">
              <div className="action-icon">📦</div>
              <h3>Bulk Upload</h3>
              <p>Upload multiple files at once with auto-ZIP</p>
            </Link>

            <div className="action-card disabled">
              <div className="action-icon">📊</div>
              <h3>Analytics</h3>
              <p>View detailed download analytics (Coming Soon)</p>
            </div>

            <div className="action-card disabled">
              <div className="action-icon">⚙️</div>
              <h3>Settings</h3>
              <p>Manage your account preferences (Coming Soon)</p>
            </div>
          </div>
        </motion.div>

        {/* Recent Files Placeholder */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.9 }}
          className="recent-files"
        >
          <h2>Recent Files</h2>
          <div className="files-placeholder">
            <div className="placeholder-icon">📁</div>
            <h3>No files yet</h3>
            <p>Your uploaded files will appear here</p>
            <Link to="/" className="btn btn-primary">
              Upload Your First File
            </Link>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Dashboard;
