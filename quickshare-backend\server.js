const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const path = require("path");
const helmet = require("helmet");
const compression = require("compression");
const rateLimit = require("express-rate-limit");
require("dotenv").config();

// Import routes
const uploadRoutes = require("./routes/upload");
const downloadRoutes = require("./routes/download");
const authRoutes = require("./routes/auth");
const batchRoutes = require("./routes/batch");

// Create Express app
const app = express();

// Security middleware
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
  })
);
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: "Too many requests from this IP, please try again later.",
  },
});

const uploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // limit each IP to 50 uploads per hour
  message: {
    success: false,
    message: "Upload limit exceeded. Please try again later.",
  },
});

app.use(limiter);

// CORS configuration
const corsOptions = {
  origin:
    process.env.NODE_ENV === "production"
      ? [process.env.FRONTEND_URL, "https://your-domain.com"]
      : [
          "http://localhost:3000",
          "http://localhost:5173",
          "http://localhost:3001",
        ],
  credentials: true,
  optionsSuccessStatus: 200,
};

app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: "10mb" })); // Parse JSON bodies
app.use(express.urlencoded({ extended: true, limit: "10mb" })); // Parse URL-encoded bodies

// Serve static files from uploads directory
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/upload", uploadLimiter, uploadRoutes);
app.use("/api/download", downloadRoutes);
app.use("/api/batch", batchRoutes);

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.status(200).json({
    success: true,
    message: "QuickShare server is running!",
    timestamp: new Date().toISOString(),
  });
});

// Root endpoint
app.get("/", (req, res) => {
  res.status(200).json({
    message: "Welcome to QuickShare API",
    endpoints: {
      upload: "/api/upload",
      download: "/api/download/:fileId",
      health: "/api/health",
    },
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error("Global error handler:", error);

  if (error.code === "LIMIT_FILE_SIZE") {
    return res.status(400).json({
      success: false,
      message: "File too large. Maximum size is 50MB.",
    });
  }

  res.status(500).json({
    success: false,
    message: "Internal server error",
    error:
      process.env.NODE_ENV === "development"
        ? error.message
        : "Something went wrong",
  });
});

// Serve frontend routes (for SPA)
app.get(
  [
    "/select/*",
    "/batch/*",
    "/dashboard",
    "/login",
    "/register",
    "/bulk-upload",
  ],
  (req, res) => {
    // In production, serve the built React app
    if (process.env.NODE_ENV === "production") {
      res.sendFile(
        path.join(__dirname, "../quickshare-frontend/dist/index.html")
      );
    } else {
      // In development, redirect to frontend dev server
      res.redirect(`http://localhost:5173${req.path}`);
    }
  }
);

// 404 handler for API routes only
app.use("/api/*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "API endpoint not found",
  });
});

// Catch all other routes and redirect to frontend
app.use("*", (req, res) => {
  if (process.env.NODE_ENV === "production") {
    res.sendFile(
      path.join(__dirname, "../quickshare-frontend/dist/index.html")
    );
  } else {
    res.redirect("http://localhost:5173");
  }
});

// Database connection
const MONGODB_URI =
  process.env.MONGODB_URI || "mongodb://localhost:27017/quickshare";

mongoose
  .connect(MONGODB_URI)
  .then(() => {
    console.log("✅ Connected to MongoDB");
  })
  .catch((error) => {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
  });

// Start server
const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`🚀 QuickShare server running on port ${PORT}`);
  console.log(`📁 Upload endpoint: http://localhost:${PORT}/api/upload`);
  console.log(
    `⬇️  Download endpoint: http://localhost:${PORT}/api/download/:fileId`
  );
});
