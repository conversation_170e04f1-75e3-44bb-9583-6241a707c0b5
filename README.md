# 📁 QuickShare - File Sharing Platform

A modern, full-stack file-sharing platform built with the MERN stack (MongoDB, Express.js, React.js, Node.js). Upload files and generate secure, shareable download links instantly!

## ✨ Features

- 🚀 **Drag & Drop Upload** - Intuitive file upload with progress tracking
- 🔗 **Instant Share Links** - Generate unique download links immediately
- 📱 **Responsive Design** - Works perfectly on desktop and mobile
- 🔒 **Secure Downloads** - UUID-based file identification
- 📊 **File Metadata** - Track file size, type, and download count
- 🎨 **Modern UI** - Beautiful gradient design with smooth animations
- 📋 **One-Click Copy** - Copy share links to clipboard instantly
- 🌐 **No Authentication Required** - Simple, hassle-free sharing

## 🛠️ Tech Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database for file metadata
- **Mongoose** - MongoDB object modeling
- **Multer** - File upload handling
- **UUID** - Unique identifier generation

### Frontend
- **React.js** - UI library
- **Vite** - Build tool and dev server
- **Axios** - HTTP client for API calls
- **Modern CSS** - Responsive design with animations

## 📋 Prerequisites

Before running this application, make sure you have:

- **Node.js** (v14 or higher)
- **MongoDB** (local installation or MongoDB Atlas)
- **npm** or **yarn** package manager

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd quickshare
```

### 2. Set Up the Backend

```bash
# Navigate to backend directory
cd quickshare-backend

# Install dependencies
npm install

# Create uploads directory
mkdir uploads

# Start MongoDB (if using local installation)
mongod

# Start the backend server
npm run dev
```

The backend will run on `http://localhost:5000`

### 3. Set Up the Frontend

```bash
# Navigate to frontend directory (in a new terminal)
cd quickshare-frontend

# Install dependencies
npm install

# Start the development server
npm run dev
```

The frontend will run on `http://localhost:5173`

## ⚙️ Configuration

### Backend Environment Variables

Create a `.env` file in the `quickshare-backend` directory:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# MongoDB Configuration (choose one)
# Local MongoDB
MONGODB_URI=mongodb://localhost:27017/quickshare

# MongoDB Atlas (cloud)
# MONGODB_URI=mongodb+srv://username:<EMAIL>/quickshare

# File Upload Configuration
MAX_FILE_SIZE=52428800  # 50MB in bytes
```

### Frontend Configuration

Update the API base URL in `src/components/FileUpload.jsx` if needed:

```javascript
const API_BASE_URL = 'http://localhost:5000'
```

## 📁 Project Structure

```
quickshare/
├── quickshare-backend/
│   ├── models/
│   │   └── File.js              # MongoDB file schema
│   ├── routes/
│   │   ├── upload.js            # File upload endpoint
│   │   └── download.js          # File download endpoint
│   ├── middleware/
│   │   └── upload.js            # Multer configuration
│   ├── uploads/                 # Uploaded files storage
│   ├── server.js                # Main server file
│   ├── package.json
│   └── .env
│
├── quickshare-frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── FileUpload.jsx   # File upload component
│   │   │   └── ShareLink.jsx    # Share link component
│   │   ├── App.jsx              # Main app component
│   │   ├── App.css              # Styles
│   │   └── main.jsx             # Entry point
│   ├── package.json
│   └── vite.config.js
│
└── README.md
```

## 🔧 API Endpoints

### Upload File
- **POST** `/api/upload`
- **Body**: FormData with file
- **Response**: File metadata and download link

### Download File
- **GET** `/api/download/:fileId`
- **Response**: File stream for download

### Get File Info
- **GET** `/api/download/:fileId/info`
- **Response**: File metadata without downloading

### Health Check
- **GET** `/api/health`
- **Response**: Server status

## 🎯 Usage

1. **Upload a File**
   - Drag and drop a file or click to browse
   - Watch the upload progress
   - Get an instant shareable link

2. **Share the Link**
   - Copy the generated download link
   - Share it with anyone
   - Recipients can download directly

3. **Download Files**
   - Click the download link
   - File downloads automatically
   - No registration required

## 🔒 Security Features

- **UUID-based file identification** - Prevents file enumeration
- **File type validation** - Configurable file restrictions
- **Size limits** - Prevent abuse with large files
- **No direct file access** - Files served through API only

## 🚀 Deployment

### Backend Deployment (Heroku/Railway/DigitalOcean)
1. Set environment variables
2. Ensure MongoDB connection
3. Deploy with `npm start`

### Frontend Deployment (Vercel/Netlify)
1. Build with `npm run build`
2. Deploy the `dist` folder
3. Update API base URL for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Built with ❤️ using the MERN stack
- Icons from Unicode emoji set
- Gradient design inspired by modern web trends

---

**Happy Sharing! 🎉**
