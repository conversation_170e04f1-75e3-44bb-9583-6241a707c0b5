import { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import FileUpload from "../components/FileUpload";
import ShareLink from "../components/ShareLink";
import { useAuth } from "../context/AuthContext";

const Home = () => {
  const { user, getFileSizeLimit, formatFileSize } = useAuth();
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleUploadSuccess = (fileData) => {
    setUploadedFile(fileData);
    setIsUploading(false);
  };

  const handleUploadStart = () => {
    setIsUploading(true);
    setUploadedFile(null);
  };

  const handleUploadError = () => {
    setIsUploading(false);
  };

  const handleReset = () => {
    setUploadedFile(null);
    setIsUploading(false);
  };

  const maxFileSize = getFileSizeLimit();
  const userType = user ? (user.isPremium ? "Premium" : "Registered") : "Guest";

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="page"
    >
      {/* Hero Section */}
      <section className="hero">
        <div className="container-center">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="hero-content"
          >
            <h1 className="hero-title">
              Share Files Like
              <span className="gradient-text"> Never Before</span>
            </h1>
            <p className="hero-subtitle">
              Upload, share, and manage your files with revolutionary features
              like QR codes, selective downloads, bulk uploads, and cross-device
              compatibility.
            </p>

            <div className="hero-features">
              <div className="feature-item">
                <span className="feature-icon">🚀</span>
                <span>Lightning Fast</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🔒</span>
                <span>Secure & Private</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">📱</span>
                <span>Cross-Device</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🌍</span>
                <span>Global Access</span>
              </div>
            </div>

            <div className="hero-stats">
              <div className="stat-item">
                <span className="stat-number">∞</span>
                <span className="stat-label">Files Shared</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">
                  {formatFileSize(maxFileSize)}
                </span>
                <span className="stat-label">Max File Size ({userType})</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">20</span>
                <span className="stat-label">Bulk Upload Limit</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Upload Section */}
      <section className="upload-section">
        <div className="container-center">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="upload-container"
          >
            {!uploadedFile && !isUploading && (
              <>
                <div className="upload-header">
                  <h2>Upload Your Files</h2>
                  <p>
                    Drag and drop files or click to browse. Get instant
                    shareable links!
                  </p>
                </div>

                <div
                  className="card"
                  style={{ padding: "2rem", marginBottom: "2rem" }}
                >
                  <FileUpload
                    onUploadStart={handleUploadStart}
                    onUploadSuccess={handleUploadSuccess}
                    onUploadError={handleUploadError}
                    endpoint="/api/upload/single"
                  />
                </div>

                <div className="upload-options">
                  <Link to="/bulk-upload" className="btn btn-secondary">
                    📦 Try Bulk Upload
                  </Link>
                  {!user && (
                    <Link to="/register" className="btn btn-primary">
                      🚀 Sign Up for More Features
                    </Link>
                  )}
                </div>
              </>
            )}

            {isUploading && (
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="card upload-status"
              >
                <div
                  className="spinner-large"
                  style={{ margin: "0 auto 1.5rem" }}
                ></div>
                <h3>Uploading your file...</h3>
                <p>Please wait while we process your upload</p>
              </motion.div>
            )}

            {uploadedFile && (
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="card"
                style={{ padding: "2rem" }}
              >
                <ShareLink fileData={uploadedFile} onReset={handleReset} />
              </motion.div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <div className="container-center">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="features-grid"
          >
            <h2 className="section-title">Revolutionary Features</h2>
            <p
              style={{
                fontSize: "1.25rem",
                color: "var(--text-secondary)",
                maxWidth: "48rem",
                margin: "0 auto",
              }}
            >
              Discover what makes QuickShare the most advanced file-sharing
              platform
            </p>
          </motion.div>

          <div className="features-list">
            {[
              {
                icon: "🎯",
                title: "Selective Download",
                desc: "Choose exactly which files to download with checkboxes",
              },
              {
                icon: "📱",
                title: "QR Code Sharing",
                desc: "Every file gets a QR code for instant mobile sharing",
              },
              {
                icon: "📦",
                title: "Bulk Upload & ZIP",
                desc: "Upload 20 files at once with auto-generated ZIP",
              },
              {
                icon: "🖼️",
                title: "Smart Thumbnails",
                desc: "Automatic thumbnail generation for images",
              },
              {
                icon: "⏰",
                title: "Auto-Expiration",
                desc: "Set custom expiration dates for security",
              },
              {
                icon: "🔐",
                title: "Password Protection",
                desc: "Add password protection to sensitive files",
              },
              {
                icon: "📊",
                title: "Download Analytics",
                desc: "Track downloads and access patterns",
              },
              {
                icon: "🌐",
                title: "Cross-Device Access",
                desc: "Access files from any device, anywhere",
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9 + index * 0.1 }}
                className="feature-card"
              >
                <span className="feature-icon">{feature.icon}</span>
                <h3>{feature.title}</h3>
                <p>{feature.desc}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      {!user && (
        <section className="cta-section">
          <div className="container-center">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1.2 }}
              className="cta-content"
            >
              <h2>Ready to Get Started?</h2>
              <p>
                Join thousands of users who trust QuickShare for their file
                sharing needs.
              </p>
              <div className="cta-buttons">
                <Link
                  to="/register"
                  className="btn btn-outline"
                  style={{ background: "white", color: "#667eea" }}
                >
                  🚀 Create Free Account
                </Link>
                <Link
                  to="/bulk-upload"
                  className="btn"
                  style={{
                    background: "transparent",
                    border: "2px solid white",
                    color: "white",
                  }}
                >
                  📦 Try Bulk Upload
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      )}
    </motion.div>
  );
};

export default Home;
