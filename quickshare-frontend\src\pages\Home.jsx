import { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import FileUpload from "../components/FileUpload";
import ShareLink from "../components/ShareLink";
import { useAuth } from "../context/AuthContext";

const Home = () => {
  const { user, getFileSizeLimit, formatFileSize } = useAuth();
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleUploadSuccess = (fileData) => {
    setUploadedFile(fileData);
    setIsUploading(false);
  };

  const handleUploadStart = () => {
    setIsUploading(true);
    setUploadedFile(null);
  };

  const handleUploadError = () => {
    setIsUploading(false);
  };

  const handleReset = () => {
    setUploadedFile(null);
    setIsUploading(false);
  };

  const maxFileSize = getFileSizeLimit();
  const userType = user ? (user.isPremium ? "Premium" : "Registered") : "Guest";

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
      {/* Hero Section */}
      <section className="hero-professional pt-20 pb-16">
        <div className="container-center">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-7xl font-black mb-6 leading-tight">
              Share Files Like
              <span className="text-gradient-primary block">Never Before</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed">
              Upload, share, and manage your files with revolutionary features
              like QR codes, selective downloads, bulk uploads, and cross-device
              compatibility.
            </p>

            <div className="flex flex-wrap justify-center gap-6 mb-12">
              <div className="flex items-center space-x-2 bg-white bg-opacity-20 rounded-full px-4 py-2">
                <span className="text-2xl">🚀</span>
                <span className="font-semibold">Lightning Fast</span>
              </div>
              <div className="flex items-center space-x-2 bg-white bg-opacity-20 rounded-full px-4 py-2">
                <span className="text-2xl">🔒</span>
                <span className="font-semibold">Secure & Private</span>
              </div>
              <div className="flex items-center space-x-2 bg-white bg-opacity-20 rounded-full px-4 py-2">
                <span className="text-2xl">📱</span>
                <span className="font-semibold">Cross-Device</span>
              </div>
              <div className="flex items-center space-x-2 bg-white bg-opacity-20 rounded-full px-4 py-2">
                <span className="text-2xl">🌍</span>
                <span className="font-semibold">Global Access</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-4xl font-black mb-2">∞</div>
                <div className="text-sm opacity-80">Files Shared</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-black mb-2">
                  {formatFileSize(maxFileSize)}
                </div>
                <div className="text-sm opacity-80">Max Size ({userType})</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-black mb-2">20</div>
                <div className="text-sm opacity-80">Bulk Upload Limit</div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Upload Section */}
      <section className="py-20">
        <div className="container-center">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            {!uploadedFile && !isUploading && (
              <>
                <div className="text-center mb-12">
                  <h2 className="text-4xl md:text-5xl font-bold mb-4 text-gray-900 dark:text-white">
                    Upload Your Files
                  </h2>
                  <p className="text-xl text-gray-600 dark:text-gray-300">
                    Drag and drop files or click to browse. Get instant
                    shareable links!
                  </p>
                </div>

                <div className="card-professional p-8 mb-8">
                  <FileUpload
                    onUploadStart={handleUploadStart}
                    onUploadSuccess={handleUploadSuccess}
                    onUploadError={handleUploadError}
                    endpoint="/api/upload/single"
                  />
                </div>

                <div className="flex flex-wrap justify-center gap-4">
                  <Link to="/bulk-upload" className="btn-secondary-pro">
                    📦 Try Bulk Upload
                  </Link>
                  {!user && (
                    <Link to="/register" className="btn-primary-pro">
                      🚀 Sign Up for More Features
                    </Link>
                  )}
                </div>
              </>
            )}

            {isUploading && (
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="card-professional p-12 text-center"
              >
                <div className="spinner-large-professional mx-auto mb-6"></div>
                <h3 className="text-2xl font-bold mb-2 text-gray-900 dark:text-white">
                  Uploading your file...
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Please wait while we process your upload
                </p>
              </motion.div>
            )}

            {uploadedFile && (
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="card-professional p-8"
              >
                <ShareLink fileData={uploadedFile} onReset={handleReset} />
              </motion.div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container-center">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4 text-gray-900 dark:text-white">
              Revolutionary Features
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Discover what makes QuickShare the most advanced file-sharing
              platform
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "🎯",
                title: "Selective Download",
                desc: "Choose exactly which files to download with checkboxes",
              },
              {
                icon: "📱",
                title: "QR Code Sharing",
                desc: "Every file gets a QR code for instant mobile sharing",
              },
              {
                icon: "📦",
                title: "Bulk Upload & ZIP",
                desc: "Upload 20 files at once with auto-generated ZIP",
              },
              {
                icon: "🖼️",
                title: "Smart Thumbnails",
                desc: "Automatic thumbnail generation for images",
              },
              {
                icon: "⏰",
                title: "Auto-Expiration",
                desc: "Set custom expiration dates for security",
              },
              {
                icon: "🔐",
                title: "Password Protection",
                desc: "Add password protection to sensitive files",
              },
              {
                icon: "📊",
                title: "Download Analytics",
                desc: "Track downloads and access patterns",
              },
              {
                icon: "🌐",
                title: "Cross-Device Access",
                desc: "Access files from any device, anywhere",
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9 + index * 0.1 }}
                className="card-professional p-6 text-center group hover:scale-105"
              >
                <div className="text-4xl mb-4 group-hover:animate-bounce-subtle">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {feature.desc}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      {!user && (
        <section className="py-20 bg-gradient-primary">
          <div className="container-center text-center">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1.2 }}
              className="max-w-3xl mx-auto"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
                Ready to Get Started?
              </h2>
              <p className="text-xl mb-8 text-white opacity-90">
                Join thousands of users who trust QuickShare for their file
                sharing needs.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link
                  to="/register"
                  className="btn-professional bg-white text-blue-600 hover:bg-gray-100"
                >
                  🚀 Create Free Account
                </Link>
                <Link
                  to="/bulk-upload"
                  className="btn-professional border-2 border-white text-white hover:bg-white hover:text-blue-600"
                >
                  📦 Try Bulk Upload
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      )}
    </div>
  );
};

export default Home;
