import { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import FileUpload from '../components/FileUpload';
import ShareLink from '../components/ShareLink';
import { useAuth } from '../context/AuthContext';

const Home = () => {
  const { user, getFileSizeLimit, formatFileSize } = useAuth();
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleUploadSuccess = (fileData) => {
    setUploadedFile(fileData);
    setIsUploading(false);
  };

  const handleUploadStart = () => {
    setIsUploading(true);
    setUploadedFile(null);
  };

  const handleUploadError = () => {
    setIsUploading(false);
  };

  const handleReset = () => {
    setUploadedFile(null);
    setIsUploading(false);
  };

  const maxFileSize = getFileSizeLimit();
  const userType = user ? (user.isPremium ? 'Premium' : 'Registered') : 'Guest';

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="page home-page"
    >
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="hero-text"
          >
            <h1 className="hero-title">
              Share Files Like Never Before
              <span className="gradient-text"> ⚡</span>
            </h1>
            <p className="hero-subtitle">
              Upload, share, and manage your files with advanced features like QR codes, 
              bulk uploads, auto-generated ZIP files, and cross-device compatibility.
            </p>
            
            <div className="hero-features">
              <div className="feature-item">
                <span className="feature-icon">🚀</span>
                <span>Lightning Fast</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🔒</span>
                <span>Secure & Private</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">📱</span>
                <span>Cross-Device</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🌍</span>
                <span>Global Access</span>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="hero-stats"
          >
            <div className="stat-item">
              <span className="stat-number">∞</span>
              <span className="stat-label">Files Shared</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{formatFileSize(maxFileSize)}</span>
              <span className="stat-label">Max File Size ({userType})</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">20</span>
              <span className="stat-label">Bulk Upload Limit</span>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Upload Section */}
      <section className="upload-section">
        <div className="container">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="upload-container"
          >
            {!uploadedFile && !isUploading && (
              <>
                <div className="upload-header">
                  <h2>Upload Your Files</h2>
                  <p>Drag and drop files or click to browse. Get instant shareable links!</p>
                </div>
                
                <FileUpload
                  onUploadStart={handleUploadStart}
                  onUploadSuccess={handleUploadSuccess}
                  onUploadError={handleUploadError}
                  endpoint="/api/upload/single"
                />

                <div className="upload-options">
                  <Link to="/bulk-upload" className="btn btn-outline">
                    📦 Try Bulk Upload
                  </Link>
                  {!user && (
                    <Link to="/register" className="btn btn-primary">
                      🚀 Sign Up for More Features
                    </Link>
                  )}
                </div>
              </>
            )}

            {isUploading && (
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="upload-status"
              >
                <div className="spinner-large"></div>
                <h3>Uploading your file...</h3>
                <p>Please wait while we process your upload</p>
              </motion.div>
            )}

            {uploadedFile && (
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
              >
                <ShareLink fileData={uploadedFile} onReset={handleReset} />
              </motion.div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <div className="container">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="features-grid"
          >
            <h2 className="section-title">Unique Features</h2>
            
            <div className="features-list">
              <div className="feature-card">
                <div className="feature-icon">📱</div>
                <h3>QR Code Sharing</h3>
                <p>Every file gets a QR code for instant mobile sharing. Scan and download!</p>
              </div>

              <div className="feature-card">
                <div className="feature-icon">📦</div>
                <h3>Bulk Upload & ZIP</h3>
                <p>Upload up to 20 files at once. Auto-generate ZIP files for easy downloading.</p>
              </div>

              <div className="feature-card">
                <div className="feature-icon">🖼️</div>
                <h3>Smart Thumbnails</h3>
                <p>Automatic thumbnail generation for images and visual file previews.</p>
              </div>

              <div className="feature-card">
                <div className="feature-icon">⏰</div>
                <h3>Auto-Expiration</h3>
                <p>Set custom expiration dates for your files. Automatic cleanup for security.</p>
              </div>

              <div className="feature-card">
                <div className="feature-icon">🔐</div>
                <h3>Password Protection</h3>
                <p>Add password protection to sensitive files for extra security.</p>
              </div>

              <div className="feature-card">
                <div className="feature-icon">📊</div>
                <h3>Download Analytics</h3>
                <p>Track download counts, locations, and access patterns for your files.</p>
              </div>

              <div className="feature-card">
                <div className="feature-icon">🌐</div>
                <h3>Cross-Device Access</h3>
                <p>Access your files from any device, anywhere in the world. True mobility.</p>
              </div>

              <div className="feature-card">
                <div className="feature-icon">⚡</div>
                <h3>Lightning Fast</h3>
                <p>Optimized servers and CDN delivery for the fastest upload and download speeds.</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      {!user && (
        <section className="cta-section">
          <div className="container">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1.0 }}
              className="cta-content"
            >
              <h2>Ready to Get Started?</h2>
              <p>Join thousands of users who trust QuickShare for their file sharing needs.</p>
              <div className="cta-buttons">
                <Link to="/register" className="btn btn-primary btn-large">
                  🚀 Create Free Account
                </Link>
                <Link to="/bulk-upload" className="btn btn-outline btn-large">
                  📦 Try Bulk Upload
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      )}
    </motion.div>
  );
};

export default Home;
