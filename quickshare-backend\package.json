{"name": "quickshare-backend", "version": "1.0.0", "description": "Backend for QuickShare file sharing platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "keywords": ["file-sharing", "mern", "express", "mongodb"], "author": "Varun", "license": "MIT", "dependencies": {"archiver": "^6.0.2", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "path": "^0.12.7", "qrcode": "^1.5.4", "sharp": "^0.33.5", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}