import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import toast from 'react-hot-toast';

const SelectiveDownload = () => {
  const { batchId } = useParams();
  const [batchData, setBatchData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedFiles, setSelectedFiles] = useState(new Set());
  const [creatingZip, setCreatingZip] = useState(false);
  const [zipDownloadLink, setZipDownloadLink] = useState(null);

  useEffect(() => {
    fetchBatchData();
  }, [batchId]);

  const fetchBatchData = async () => {
    try {
      const response = await axios.get(`/api/batch/${batchId}`);
      if (response.data.success) {
        setBatchData(response.data.batch);
      } else {
        setError('Batch not found');
      }
    } catch (error) {
      console.error('Error fetching batch data:', error);
      setError('Failed to load batch data');
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimetype) => {
    if (mimetype.startsWith('image/')) return '🖼️';
    if (mimetype.startsWith('video/')) return '🎥';
    if (mimetype.startsWith('audio/')) return '🎵';
    if (mimetype.includes('pdf')) return '📄';
    if (mimetype.includes('zip') || mimetype.includes('rar')) return '📦';
    return '📁';
  };

  const toggleFileSelection = (fileId) => {
    const newSelected = new Set(selectedFiles);
    if (newSelected.has(fileId)) {
      newSelected.delete(fileId);
    } else {
      newSelected.add(fileId);
    }
    setSelectedFiles(newSelected);
  };

  const selectAll = () => {
    if (selectedFiles.size === batchData.files.length) {
      setSelectedFiles(new Set());
    } else {
      setSelectedFiles(new Set(batchData.files.map(f => f.id)));
    }
  };

  const getSelectedSize = () => {
    return batchData.files
      .filter(file => selectedFiles.has(file.id))
      .reduce((sum, file) => sum + file.size, 0);
  };

  const createSelectiveZip = async () => {
    if (selectedFiles.size === 0) {
      toast.error('Please select at least one file');
      return;
    }

    setCreatingZip(true);
    try {
      const response = await axios.post(`/api/batch/${batchId}/selective-zip`, {
        selectedFileIds: Array.from(selectedFiles)
      });

      if (response.data.success) {
        setZipDownloadLink(response.data.zipDownloadLink);
        toast.success(`ZIP created with ${selectedFiles.size} files!`);
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error('ZIP creation error:', error);
      toast.error(error.response?.data?.message || 'Failed to create ZIP');
    } finally {
      setCreatingZip(false);
    }
  };

  const downloadSelected = () => {
    if (selectedFiles.size === 0) {
      toast.error('Please select at least one file');
      return;
    }

    // Download each selected file individually
    batchData.files
      .filter(file => selectedFiles.has(file.id))
      .forEach((file, index) => {
        setTimeout(() => {
          window.open(file.downloadLink, '_blank');
        }, index * 500); // Stagger downloads to avoid browser blocking
      });

    toast.success(`Downloading ${selectedFiles.size} files...`);
  };

  if (loading) {
    return (
      <div className="page selective-download-page">
        <div className="container">
          <div className="loading-state">
            <div className="spinner-large"></div>
            <p>Loading files...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !batchData) {
    return (
      <div className="page selective-download-page">
        <div className="container">
          <div className="error-state">
            <div className="error-icon">❌</div>
            <h2>Batch Not Found</h2>
            <p>The requested batch of files could not be found or may have expired.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="page selective-download-page"
    >
      <div className="container">
        <div className="selective-header">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <h1>🎯 Selective Download</h1>
            <p>Choose which files you want to download from this batch</p>
            {batchData.description && (
              <div className="batch-description">
                <strong>Description:</strong> {batchData.description}
              </div>
            )}
          </motion.div>
        </div>

        {/* Selection Controls */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="selection-controls"
        >
          <div className="selection-info">
            <span className="selected-count">
              {selectedFiles.size} of {batchData.files.length} files selected
            </span>
            {selectedFiles.size > 0 && (
              <span className="selected-size">
                ({formatFileSize(getSelectedSize())})
              </span>
            )}
          </div>
          
          <div className="selection-actions">
            <button onClick={selectAll} className="btn btn-outline">
              {selectedFiles.size === batchData.files.length ? 'Deselect All' : 'Select All'}
            </button>
            
            <button 
              onClick={downloadSelected}
              disabled={selectedFiles.size === 0}
              className="btn btn-primary"
            >
              ⬇️ Download Selected ({selectedFiles.size})
            </button>
            
            <button 
              onClick={createSelectiveZip}
              disabled={selectedFiles.size === 0 || creatingZip}
              className="btn btn-secondary"
            >
              {creatingZip ? (
                <>
                  <div className="spinner"></div>
                  Creating ZIP...
                </>
              ) : (
                `📦 Create ZIP (${selectedFiles.size})`
              )}
            </button>
          </div>
        </motion.div>

        {/* ZIP Download Link */}
        <AnimatePresence>
          {zipDownloadLink && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="zip-download-section"
            >
              <div className="zip-success">
                <h3>📦 ZIP File Ready!</h3>
                <p>Your selected files have been packaged into a ZIP file</p>
                <a
                  href={zipDownloadLink}
                  className="btn btn-primary btn-large"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  📥 Download ZIP File
                </a>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Files Grid */}
        <div className="files-selection-grid">
          {batchData.files.map((file, index) => (
            <motion.div
              key={file.id}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6 + index * 0.05 }}
              className={`file-selection-card ${selectedFiles.has(file.id) ? 'selected' : ''}`}
              onClick={() => toggleFileSelection(file.id)}
            >
              <div className="file-checkbox">
                <input
                  type="checkbox"
                  checked={selectedFiles.has(file.id)}
                  onChange={() => toggleFileSelection(file.id)}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              
              <div className="file-icon">{getFileIcon(file.mimetype)}</div>
              
              <div className="file-info">
                <h3>{file.originalName}</h3>
                <p>{formatFileSize(file.size)}</p>
                <span className="file-type">{file.mimetype}</span>
                {file.downloadCount > 0 && (
                  <span className="download-count">
                    Downloaded {file.downloadCount} times
                  </span>
                )}
              </div>
              
              <div className="file-actions">
                <a
                  href={file.downloadLink}
                  className="btn btn-small"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={(e) => e.stopPropagation()}
                >
                  ⬇️ Download
                </a>
                {file.qrCode && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      const newWindow = window.open();
                      newWindow.document.write(`
                        <html>
                          <head><title>QR Code - ${file.originalName}</title></head>
                          <body style="text-align: center; padding: 20px; font-family: Arial, sans-serif;">
                            <h2>${file.originalName}</h2>
                            <img src="${file.qrCode}" alt="QR Code" style="max-width: 300px; border: 1px solid #ddd; padding: 20px;">
                            <p>Scan this QR code with your mobile device to download the file</p>
                            <p style="color: #666; font-size: 14px;">Size: ${formatFileSize(file.size)}</p>
                          </body>
                        </html>
                      `);
                    }}
                    className="btn btn-small btn-outline"
                  >
                    📱 QR
                  </button>
                )}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Footer Info */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 1.0 }}
          className="selective-footer"
        >
          <div className="footer-stats">
            <div className="stat-item">
              <span className="stat-number">{batchData.totalFiles}</span>
              <span className="stat-label">Total Files</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{formatFileSize(batchData.totalSize)}</span>
              <span className="stat-label">Total Size</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">
                {new Date(batchData.uploadDate).toLocaleDateString()}
              </span>
              <span className="stat-label">Uploaded</span>
            </div>
          </div>
          
          <div className="saas-features">
            <h3>🚀 QuickShare Pro Features</h3>
            <ul>
              <li>✅ Selective file downloads with checkboxes</li>
              <li>✅ Custom ZIP creation from selected files</li>
              <li>✅ QR codes for mobile sharing</li>
              <li>✅ Download analytics and tracking</li>
              <li>✅ Batch management and organization</li>
            </ul>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default SelectiveDownload;
