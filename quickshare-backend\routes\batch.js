const express = require('express');
const path = require('path');
const fs = require('fs');
const archiver = require('archiver');
const { v4: uuidv4 } = require('uuid');
const File = require('../models/File');

const router = express.Router();

// GET /api/batch/:batchId - Get batch information
router.get('/:batchId', async (req, res) => {
  try {
    const { batchId } = req.params;

    // Find all files in this batch
    const files = await File.find({ 
      batchId: batchId,
      status: 'active'
    }).sort({ uploadDate: -1 });

    if (files.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Batch not found or expired'
      });
    }

    // Get batch metadata from first file
    const batchInfo = {
      batchId: batchId,
      uploadDate: files[0].uploadDate,
      description: files[0].description || '',
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      files: files.map(file => ({
        id: file.fileId,
        originalName: file.originalName,
        size: file.size,
        mimetype: file.mimetype,
        category: file.category,
        downloadLink: `${req.protocol}://${req.get('host')}/api/download/${file.fileId}`,
        qrCode: file.qrCode,
        thumbnail: file.thumbnail,
        downloadCount: file.downloadCount
      }))
    };

    res.status(200).json({
      success: true,
      batch: batchInfo
    });

  } catch (error) {
    console.error('Batch fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// POST /api/batch/:batchId/selective-zip - Create ZIP from selected files
router.post('/:batchId/selective-zip', async (req, res) => {
  try {
    const { batchId } = req.params;
    const { selectedFileIds } = req.body;

    if (!selectedFileIds || selectedFileIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files selected'
      });
    }

    // Find selected files
    const files = await File.find({ 
      batchId: batchId,
      fileId: { $in: selectedFileIds },
      status: 'active'
    });

    if (files.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No valid files found'
      });
    }

    // Create temporary ZIP file
    const zipFileId = uuidv4();
    const zipFilename = `selected_files_${Date.now()}.zip`;
    const zipPath = path.join('uploads/files', zipFilename);
    
    const output = fs.createWriteStream(zipPath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    // Handle archive events
    output.on('close', async () => {
      try {
        // Create ZIP file record
        const zipFile = new File({
          fileId: zipFileId,
          originalName: zipFilename,
          filename: zipFilename,
          filePath: zipPath,
          size: archive.pointer(),
          mimetype: 'application/zip',
          description: `Selected files from batch ${batchId}`,
          batchId: batchId,
          category: 'archive',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        });
        
        await zipFile.save();
        
        const downloadLink = `${req.protocol}://${req.get('host')}/api/download/${zipFileId}`;
        
        res.status(200).json({
          success: true,
          message: `ZIP created with ${files.length} selected files`,
          zipDownloadLink: downloadLink,
          selectedFiles: files.length,
          totalSize: archive.pointer()
        });
      } catch (error) {
        console.error('ZIP file save error:', error);
        res.status(500).json({
          success: false,
          message: 'Error saving ZIP file'
        });
      }
    });

    archive.on('error', (err) => {
      console.error('Archive error:', err);
      res.status(500).json({
        success: false,
        message: 'Error creating ZIP file'
      });
    });

    archive.pipe(output);
    
    // Add selected files to ZIP
    for (const file of files) {
      if (fs.existsSync(file.filePath)) {
        archive.file(file.filePath, { name: file.originalName });
      }
    }
    
    await archive.finalize();

  } catch (error) {
    console.error('Selective ZIP error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during ZIP creation',
      error: error.message
    });
  }
});

// GET /api/batch/:batchId/analytics - Get batch analytics (SAAS FEATURE)
router.get('/:batchId/analytics', async (req, res) => {
  try {
    const { batchId } = req.params;

    // Find all files in this batch
    const files = await File.find({ 
      batchId: batchId,
      status: 'active'
    });

    if (files.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Batch not found'
      });
    }

    // Calculate analytics
    const analytics = {
      batchId: batchId,
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      totalDownloads: files.reduce((sum, file) => sum + file.downloadCount, 0),
      uploadDate: files[0].uploadDate,
      
      // File type breakdown
      fileTypes: {},
      
      // Download analytics
      downloadHistory: [],
      
      // Popular files
      mostDownloaded: files
        .sort((a, b) => b.downloadCount - a.downloadCount)
        .slice(0, 5)
        .map(file => ({
          name: file.originalName,
          downloads: file.downloadCount,
          size: file.size
        })),
      
      // Geographic data (from download history)
      countries: {},
      
      // Time-based analytics
      downloadsByDay: {}
    };

    // Process file types
    files.forEach(file => {
      const category = file.category || 'other';
      analytics.fileTypes[category] = (analytics.fileTypes[category] || 0) + 1;
    });

    // Process download history
    files.forEach(file => {
      if (file.downloads && file.downloads.length > 0) {
        file.downloads.forEach(download => {
          analytics.downloadHistory.push({
            fileName: file.originalName,
            downloadedAt: download.downloadedAt,
            country: download.country || 'Unknown',
            ip: download.ip
          });
          
          // Count countries
          const country = download.country || 'Unknown';
          analytics.countries[country] = (analytics.countries[country] || 0) + 1;
          
          // Count downloads by day
          const day = download.downloadedAt.toISOString().split('T')[0];
          analytics.downloadsByDay[day] = (analytics.downloadsByDay[day] || 0) + 1;
        });
      }
    });

    res.status(200).json({
      success: true,
      analytics: analytics
    });

  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// DELETE /api/batch/:batchId - Delete entire batch (SAAS FEATURE)
router.delete('/:batchId', async (req, res) => {
  try {
    const { batchId } = req.params;

    // Find all files in this batch
    const files = await File.find({ batchId: batchId });

    if (files.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Batch not found'
      });
    }

    // Delete physical files
    let deletedFiles = 0;
    for (const file of files) {
      try {
        if (fs.existsSync(file.filePath)) {
          fs.unlinkSync(file.filePath);
        }
        if (file.thumbnail && fs.existsSync(file.thumbnail)) {
          fs.unlinkSync(file.thumbnail);
        }
        deletedFiles++;
      } catch (fileError) {
        console.error(`Error deleting file ${file.filePath}:`, fileError);
      }
    }

    // Update database records
    await File.updateMany(
      { batchId: batchId },
      { status: 'deleted' }
    );

    res.status(200).json({
      success: true,
      message: `Batch deleted successfully`,
      deletedFiles: deletedFiles,
      totalFiles: files.length
    });

  } catch (error) {
    console.error('Batch deletion error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during batch deletion',
      error: error.message
    });
  }
});

module.exports = router;
